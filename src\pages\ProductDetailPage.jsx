import React, { useState, useEffect, useMemo } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { Heart, ShoppingCart, ChevronLeft, ChevronRight, CheckCircle, Star } from 'lucide-react';
import ProductCard from '@/components/ProductCard';
import { db } from '@/firebase.js';
import { collection, getDocs, doc, getDoc } from 'firebase/firestore';

const ProductDetailPage = () => {
  const { productId } = useParams();
  const { toast } = useToast();
  const [product, setProduct] = useState(null);
  const [allProducts, setAllProducts] = useState([]);
  const [selectedOptions, setSelectedOptions] = useState({});
  const [quantity, setQuantity] = useState(1);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const querySnapshot = await getDocs(collection(db, "products"));
        const products = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        setAllProducts(products);
        
        // Find the specific product
        const foundProduct = products.find(p => p.id === productId || p.id.toString() === productId);
        setProduct(foundProduct);
        
        // Set default options if product exists
        if (foundProduct && foundProduct.options) {
          const defaultSelections = {};
          Object.keys(foundProduct.options).forEach(key => {
            const defaultOption = foundProduct.options[key].find(opt => opt.default);
            if (defaultOption) {
              defaultSelections[key] = defaultOption.name;
            } else if (foundProduct.options[key].length > 0) {
              defaultSelections[key] = foundProduct.options[key][0].name;
            }
          });
          setSelectedOptions(defaultSelections);
        }
      } catch (error) {
        console.error("Error fetching products:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [productId]);

  // Fallback data in case Firestore isn't set up yet
  const allProductsData = [
    { 
      id: 1, 
      name: "Rose Crochet Bag", 
      category: "Crochet", 
      price: "28.00", 
      description: "Elegant and spacious rose-colored crochet bag, perfect for daily essentials or a chic outing. Handcrafted with soft, durable yarn.", 
      longDescription: "This beautifully handcrafted crochet bag features an intricate stitch pattern in a lovely rose pastel hue. It's lined with a complementary fabric and includes an inner pocket for small items. The sturdy straps ensure comfortable carrying. A versatile accessory that adds a touch of handmade charm to any outfit.",
      tags: ["bag", "fashion", "handmade", "pastel"],
      images: [
        "crochet_bag_rose_main", 
        "crochet_bag_rose_detail", 
        "crochet_bag_rose_lifestyle"
      ],
      options: {
        styles: [
          { name: "Pastel Dreams", default: true },
          { name: "Ocean Hues" },
          { name: "Sunset Glow" }
        ],
        framing: [
          { name: "Unframed", default: true },
          { name: "Floating Frame (White)"}
        ]
      },
      stock: 5,
      rating: 5.0,
      reviews: 12,
      sku: "CNV001-PAS-UNF"
    },
  ];

  // Use fallback data if Firestore data isn't available yet
  const placeholderRelatedProducts = useMemo(() => {
    return allProducts.length > 0 ? allProducts.slice(0, 3) : allProductsData.slice(0, 3);
  }, [allProducts]);

  const handleOptionChange = (optionType, value) => {
    setSelectedOptions(prev => ({ ...prev, [optionType]: value }));
  };

  const handleQuantityChange = (amount) => {
    setQuantity(prev => Math.max(1, Math.min(prev + amount, product?.stock || 1)));
  };
  
  const handleAddToCart = () => {
    toast({
      title: "💖 Added to Cart!",
      description: `${product.name} (${Object.values(selectedOptions).join(', ')}) x${quantity} is now in your cart.`,
      duration: 3000,
      action: <CheckCircle className="text-green-500" />,
    });
  };

  const handleWishlist = () => {
     toast({
      title: "❤️ Added to Wishlist!",
      description: `${product.name} has been added to your wishlist.`,
      duration: 2000,
    });
  }

  const nextImage = () => {
    setCurrentImageIndex(prev => (prev + 1) % (product.images?.length || 1));
  };

  const prevImage = () => {
    setCurrentImageIndex(prev => (prev - 1 + (product.images?.length || 1)) % (product.images?.length || 1));
  };
  
  const pageTransition = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0, transition: { duration: 0.5, ease: "easeOut" } },
    exit: { opacity: 0, y: -20, transition: { duration: 0.3, ease: "easeIn" } }
  };
  
  const imageTransition = {
    initial: { opacity: 0, scale: 0.95 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.95 }
  };

  if (loading) {
    return <div className="container mx-auto text-center py-20 text-xl text-pastel-accent">Loading product details...</div>;
  }

  if (!product) {
    return <div className="container mx-auto text-center py-20 text-xl text-pastel-accent">Product not found</div>;
  }

  return (
    <motion.div 
      variants={pageTransition}
      initial="initial"
      animate="animate"
      exit="exit"
      className="container mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-16 animate-fade-in"
    >
      <div className="grid md:grid-cols-2 gap-8 md:gap-12 items-start">
        {/* Image Gallery */}
        <motion.div 
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="relative"
        >
          <AnimatePresence mode="wait">
            <motion.div 
              key={currentImageIndex}
              variants={imageTransition}
              initial="initial"
              animate="animate"
              exit="exit"
              className="aspect-square w-full rounded-xl shadow-2xl overflow-hidden bg-pastel-light"
            >
              <img  
                className="w-full h-full object-cover" 
                alt={`${product.name} - view ${currentImageIndex + 1}`}
                src={`https://images.unsplash.com/photo-1580928087639-6bfb993701a0?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=600&h=600&fit=crop&ixid=M3w2Mzg4NTR8MHwxfHJhbmRvbXx8fHx8fHx8fDE3MTcxNzE0Njd8`} 
              />
            </motion.div>
          </AnimatePresence>
          {product.images && product.images.length > 1 && (
            <>
              <Button variant="ghost" size="icon" onClick={prevImage} className="absolute top-1/2 left-2 -translate-y-1/2 bg-white/70 hover:bg-pastel-medium text-pastel-accent rounded-full backdrop-blur-sm z-10">
                <ChevronLeft size={28} />
              </Button>
              <Button variant="ghost" size="icon" onClick={nextImage} className="absolute top-1/2 right-2 -translate-y-1/2 bg-white/70 hover:bg-pastel-medium text-pastel-accent rounded-full backdrop-blur-sm z-10">
                <ChevronRight size={28} />
              </Button>
              <div className="mt-4 flex space-x-2 justify-center">
                {product.images.map((img, index) => (
                  <button 
                    key={index} 
                    onClick={() => setCurrentImageIndex(index)} 
                    className={`w-16 h-16 rounded-md overflow-hidden border-2 ${index === currentImageIndex ? 'border-pastel-dark ring-2 ring-pastel-dark' : 'border-transparent hover:border-pastel-medium'} transition-all`}
                  >
                    <img  className="w-full h-full object-cover" alt={`Thumbnail ${index + 1}`} src={`https://images.unsplash.com/photo-1516762689617-e1cffcef479d?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=100&h=100&fit=crop&ixid=M3w2Mzg4NTR8MHwxfHJhbmRvbXx8fHx8fHx8fDE3MTcxNzE1MDV8`} />
                  </button>
                ))}
              </div>
            </>
          )}
        </motion.div>

        {/* Product Details */}
        <motion.div 
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="space-y-6"
        >
          <span className="text-sm text-pastel-dark font-medium uppercase tracking-wider">{product.category}</span>
          <h1 className="text-3xl md:text-4xl font-bold text-pastel-accent">{product.name}</h1>
          <div className="flex items-center space-x-2">
            <div className="flex text-yellow-400">
              {[...Array(Math.floor(product.rating))].map((_, i) => <Star key={i} size={20} className="fill-current" />)}
              {product.rating % 1 !== 0 && <Star size={20} className="fill-current opacity-50" />} 
              {[...Array(5 - Math.ceil(product.rating))].map((_, i) => <Star key={`empty-${i}`} size={20} className="text-gray-300" />)}
            </div>
            <span className="text-sm text-pastel-accent/70">({product.reviews} reviews)</span>
          </div>
          <p className="text-2xl font-semibold text-pastel-dark">${product.price}</p>
          <p className="text-pastel-accent/80 leading-relaxed">{product.description}</p>

          {/* Options */}
          {product.options && Object.keys(product.options).map(optionType => (
            <div key={optionType} className="space-y-2">
              <h3 className="text-md font-semibold text-pastel-accent capitalize">{optionType}: <span className="font-normal text-pastel-accent/90">{selectedOptions[optionType]}</span></h3>
              <div className="flex flex-wrap gap-2">
                {product.options[optionType].map(opt => (
                  <Button
                    key={opt.name}
                    variant={selectedOptions[optionType] === opt.name ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleOptionChange(optionType, opt.name)}
                    className={`
                      ${selectedOptions[optionType] === opt.name ? 'bg-pastel-dark text-white border-pastel-dark' : 'border-pastel-medium text-pastel-accent hover:bg-pastel-light hover:border-pastel-dark'}
                      ${optionType === 'colors' ? 'h-8 w-8 p-0 rounded-full' : ''}
                    `}
                    style={optionType === 'colors' ? { backgroundColor: opt.value } : {}}
                    aria-label={optionType === 'colors' ? `Select color ${opt.name}`: `Select ${optionType} ${opt.name}`}
                  >
                    {optionType !== 'colors' && opt.name}
                    {optionType === 'colors' && selectedOptions[optionType] === opt.name && <CheckCircle size={16} className="text-white/70" />}
                  </Button>
                ))}
              </div>
            </div>
          ))}

          {/* Quantity Selector */}
          <div className="flex items-center space-x-4">
            <h3 className="text-md font-semibold text-pastel-accent">Quantity:</h3>
            <div className="flex items-center border border-pastel-medium rounded-md">
              <Button variant="ghost" size="sm" onClick={() => handleQuantityChange(-1)} className="px-3 text-pastel-accent hover:bg-pastel-light rounded-r-none">-</Button>
              <span className="px-4 text-pastel-accent">{quantity}</span>
              <Button variant="ghost" size="sm" onClick={() => handleQuantityChange(1)} className="px-3 text-pastel-accent hover:bg-pastel-light rounded-l-none">+</Button>
            </div>
            <span className="text-sm text-pastel-accent/70">{product.stock} in stock</span>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 pt-4">
            <Button size="lg" onClick={handleAddToCart} className="flex-1 bg-pastel-accent text-pastel-bg hover:bg-pastel-accent/90 shadow-lg">
              <ShoppingCart size={20} className="mr-2" /> Add to Cart
            </Button>
            <Button size="lg" variant="outline" onClick={handleWishlist} className="flex-1 border-pastel-dark text-pastel-accent hover:bg-pastel-dark hover:text-pastel-bg shadow-md">
              <Heart size={20} className="mr-2" /> Add to Wishlist
            </Button>
          </div>
           <div className="text-sm text-pastel-accent/70">SKU: {product.sku.replace(/COLOR|SIZE|SCENT/g, (match) => selectedOptions[match.toLowerCase()+"s"]?.toUpperCase() || match)}</div>
        </motion.div>
      </div>

      {/* Long Description / Tabs */}
      <motion.div 
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="mt-16 pt-8 border-t border-pastel-medium/50"
      >
        <h2 className="text-2xl font-semibold text-pastel-accent mb-4">Product Details</h2>
        <div className="prose prose-lg max-w-none text-pastel-accent/80 leading-relaxed">
          <p>{product.longDescription}</p>
        </div>
      </motion.div>
      
      {/* Related Products */}
      <motion.section 
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
        className="mt-16 pt-8 border-t border-pastel-medium/50"
      >
        <h2 className="text-2xl md:text-3xl font-bold text-pastel-accent text-center mb-12">
          You Might Also Like
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {placeholderRelatedProducts.filter(p => p.id !== product.id).map((relatedProduct, index) => (
            <motion.div 
              key={relatedProduct.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: index * 0.1 }}
            >
              <ProductCard product={relatedProduct} />
            </motion.div>
          ))}
        </div>
      </motion.section>
    </motion.div>
  );
};

export default ProductDetailPage;

