import React, { useState, useEffect, useMemo } from 'react';
import { useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import ProductCard from '@/components/ProductCard';
import { Button } from '@/components/ui/button';
import { Filter, X } from 'lucide-react';
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { useToast } from "@/components/ui/use-toast";
import { db } from '../firebase.js';
import { collection, getDocs } from 'firebase/firestore';
import { addSampleProducts } from '../utils/sampleProducts.js';
import { debugFirestore, checkFirebaseConfig } from '../utils/debugFirestore.js';
import { fetchAllProducts } from '../utils/fetchProducts.js';

const categories = ['All', 'Crochet', 'Candles', 'Crafts', 'Clay', 'Concrete', 'Canvas'];

const ShopPage = () => {
  const location = useLocation();
  const { toast } = useToast();
  const [allProducts, setAllProducts] = useState([]);
  const [selectedCategories, setSelectedCategories] = useState(['All']);
  const [priceRange, setPriceRange] = useState([0, 50]);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [addingProducts, setAddingProducts] = useState(false);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        setError(null);
        console.log('🔍 Fetching products from Firestore with flexible structure detection...');

        // Try the flexible fetching first
        const result = await fetchAllProducts();

        if (result.success) {
          console.log(`✅ Successfully fetched ${result.totalProducts} products from ${result.totalDocuments} documents`);
          setAllProducts(result.products);

          if (result.products.length === 0) {
            console.log('⚠️ No products found. Documents exist but no product data extracted.');
          }
        } else {
          console.error('❌ Flexible fetch failed, trying simple approach...');

          // Fallback to simple approach
          const querySnapshot = await getDocs(collection(db, "products"));
          const products = querySnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }));

          console.log('📦 Simple fetch result:', products);
          setAllProducts(products);
        }
      } catch (err) {
        console.error('❌ Error fetching products:', err);
        setError('Failed to load products. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  const handleAddSampleProducts = async () => {
    try {
      setAddingProducts(true);
      const result = await addSampleProducts();

      if (result.success) {
        toast({
          title: "✅ Success!",
          description: result.message,
          duration: 3000,
        });

        // Refresh the products list using flexible fetching
        const result = await fetchAllProducts();
        if (result.success) {
          setAllProducts(result.products);
        } else {
          // Fallback to simple approach
          const querySnapshot = await getDocs(collection(db, "products"));
          const products = querySnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }));
          setAllProducts(products);
        }
      } else {
        toast({
          title: "❌ Error",
          description: result.message,
          duration: 5000,
        });
      }
    } catch (err) {
      console.error('Error adding sample products:', err);
      toast({
        title: "❌ Error",
        description: "Failed to add sample products. Please try again.",
        duration: 5000,
      });
    } finally {
      setAddingProducts(false);
    }
  };

  const handleDebugFirestore = async () => {
    console.log('🔍 Running Firestore Debug...');

    // Check Firebase config first
    const configResult = checkFirebaseConfig();
    console.log('🔧 Config result:', configResult);

    // Run debug
    const debugResult = await debugFirestore();
    console.log('🔍 Debug result:', debugResult);

    toast({
      title: "🔍 Debug Complete",
      description: `Found ${debugResult.productsCount || 0} products. Check console for details.`,
      duration: 5000,
    });
  };

  const handleTestFlexibleFetch = async () => {
    console.log('🧪 Testing flexible product fetching...');

    const result = await fetchAllProducts();
    console.log('🧪 Flexible fetch result:', result);

    if (result.success) {
      setAllProducts(result.products);
      toast({
        title: "🧪 Flexible Fetch Complete",
        description: `Found ${result.totalProducts} products from ${result.totalDocuments} documents`,
        duration: 5000,
      });
    } else {
      toast({
        title: "❌ Flexible Fetch Failed",
        description: result.error,
        duration: 5000,
      });
    }
  };

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const categoryFromUrl = params.get('category');
    if (categoryFromUrl && categories.map(c => c.toLowerCase()).includes(categoryFromUrl)) {
      const capitalizedCategory = categoryFromUrl.charAt(0).toUpperCase() + categoryFromUrl.slice(1);
      setSelectedCategories([capitalizedCategory]);
    } else {
      setSelectedCategories(['All']);
    }
  }, [location.search]);

  const handleCategoryChange = (category) => {
    setSelectedCategories(prev => {
      if (category === 'All') return ['All'];
      const newCategories = prev.includes('All') ? [] : [...prev];
      if (newCategories.includes(category)) {
        const filtered = newCategories.filter(c => c !== category);
        return filtered.length === 0 ? ['All'] : filtered;
      } else {
        return [...newCategories, category];
      }
    });
  };

  const filteredProducts = useMemo(() => {
    console.log('🔍 Filtering products...');
    console.log('📦 All products for filtering:', allProducts);
    console.log('🏷️ Selected categories:', selectedCategories);
    console.log('💰 Price range:', priceRange);

    const filtered = allProducts.filter(product => {
      console.log('🔍 Checking product:', product);

      // Handle nested categories like "Crochet > Bags"
      const productMainCategory = product.category ? product.category.split(' > ')[0] : '';
      const categoryMatch = selectedCategories.includes('All') ||
                           selectedCategories.includes(product.category) ||
                           selectedCategories.includes(productMainCategory);

      console.log(`🏷️ Category check for "${product.name}": category="${product.category}", mainCategory="${productMainCategory}", match=${categoryMatch}`);

      const productPrice = parseFloat(product.price);
      const priceMatch = !isNaN(productPrice) && productPrice >= priceRange[0] && productPrice <= priceRange[1];

      console.log(`💰 Price check for "${product.name}": price="${product.price}", parsed=${productPrice}, range=[${priceRange[0]}, ${priceRange[1]}], match=${priceMatch}`);

      const shouldInclude = categoryMatch && priceMatch;
      console.log(`✅ Include "${product.name}": ${shouldInclude}`);

      return shouldInclude;
    });

    console.log('📦 Filtered products:', filtered);
    return filtered;
  }, [selectedCategories, priceRange, allProducts]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { staggerChildren: 0.1 } }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { type: 'spring', stiffness: 100 } }
  };

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-16 animate-fade-in">
      <motion.h1 
        initial={{ opacity:0, y: -30 }}
        animate={{ opacity:1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className="text-4xl md:text-5xl font-bold text-pastel-accent text-center mb-12"
      >
        Our Handmade Collection
      </motion.h1>

      <div className="flex flex-col md:flex-row gap-8">
        <motion.aside 
          initial={{ opacity:0, x: -50 }}
          animate={{ opacity:1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className={`md:w-1/4 bg-pastel-light p-6 rounded-xl shadow-lg md:sticky md:top-24 h-fit ${isFilterOpen ? 'block' : 'hidden'} md:block`}
        >
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-semibold text-pastel-accent">Filters</h2>
            <Button variant="ghost" size="icon" className="md:hidden text-pastel-accent" onClick={() => setIsFilterOpen(false)}>
              <X size={24} />
            </Button>
          </div>
          
          <div className="mb-8">
            <h3 className="text-lg font-medium text-pastel-accent mb-3">Categories</h3>
            <div className="space-y-2">
              {categories.map((category) => (
                <div key={category} className="flex items-center space-x-2">
                  <Checkbox
                    id={category}
                    checked={selectedCategories.includes(category)}
                    onCheckedChange={() => handleCategoryChange(category)}
                    className="data-[state=checked]:bg-pastel-dark data-[state=checked]:text-pastel-bg border-pastel-medium"
                  />
                  <Label htmlFor={category} className="text-pastel-accent/90 cursor-pointer hover:text-pastel-dark">{category}</Label>
                </div>
              ))}
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium text-pastel-accent mb-3">Price Range</h3>
            <Slider
              defaultValue={[0, 50]}
              min={0}
              max={50}
              step={1}
              value={priceRange}
              onValueChange={setPriceRange}
              className="[&>span:first-child]:h-1 [&>span:first-child]:bg-pastel-medium [&_[role=slider]]:bg-pastel-dark [&_[role=slider]]:border-pastel-dark [&_[role=slider]]:shadow-md"
            />
            <div className="flex justify-between text-sm text-pastel-accent/80 mt-2">
              <span>${priceRange[0]}</span>
              <span>${priceRange[1]}</span>
            </div>
          </div>
        </motion.aside>

        <main className="md:w-3/4">
          <div className="md:hidden mb-6 flex justify-between">
            <Button
              variant="outline"
              size="sm"
              className="border-pastel-dark text-pastel-accent"
              onClick={handleDebugFirestore}
            >
              🔍 Debug
            </Button>
            <Button variant="outline" className="border-pastel-dark text-pastel-accent" onClick={() => setIsFilterOpen(true)}>
              <Filter size={18} className="mr-2" /> Filters
            </Button>
          </div>

          {/* Debug buttons for desktop */}
          <div className="hidden md:block mb-6 space-y-2">
            <Button
              variant="outline"
              size="sm"
              className="border-pastel-dark text-pastel-accent w-full"
              onClick={handleDebugFirestore}
            >
              🔍 Debug Firestore Connection
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="border-blue-500 text-blue-600 w-full"
              onClick={handleTestFlexibleFetch}
            >
              🧪 Test Flexible Fetch
            </Button>

            {/* Debug Info Display */}
            <div className="mt-4 p-3 bg-gray-100 rounded text-xs">
              <div><strong>Total Products:</strong> {allProducts.length}</div>
              <div><strong>Filtered Products:</strong> {filteredProducts.length}</div>
              <div><strong>Selected Categories:</strong> {selectedCategories.join(', ')}</div>
              <div><strong>Price Range:</strong> ${priceRange[0]} - ${priceRange[1]}</div>
              {allProducts.length > 0 && (
                <div className="mt-2">
                  <strong>Sample Product:</strong>
                  <pre className="text-xs bg-white p-1 mt-1 rounded overflow-auto max-h-20">
                    {JSON.stringify(allProducts[0], null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>

          {loading ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-20"
            >
              <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-pastel-accent"></div>
              <p className="mt-4 text-lg text-pastel-accent/70">Loading products...</p>
            </motion.div>
          ) : error ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-20"
            >
              <p className="text-xl text-red-500 mb-4">{error}</p>
              <Button
                onClick={() => window.location.reload()}
                className="bg-pastel-dark text-white hover:bg-pastel-accent"
              >
                Try Again
              </Button>
            </motion.div>
          ) : filteredProducts.length > 0 ? (
            <>
              {console.log('🎨 Rendering products grid with', filteredProducts.length, 'products')}
              <motion.div
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8"
              >
                {filteredProducts.map((product, index) => {
                  console.log(`🎨 Rendering product ${index}:`, product);
                  return (
                    <motion.div key={product.id} variants={itemVariants} custom={index} className="animate-slide-in-up" style={{animationDelay: `${index * 0.05}s`}}>
                      <ProductCard product={product} />
                    </motion.div>
                  );
                })}
              </motion.div>
            </>
          ) : allProducts.length === 0 ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              className="text-center py-20"
            >
              <p className="text-xl text-pastel-accent/70 mb-4">No products found in the database.</p>
              <p className="text-sm text-pastel-accent/50 mb-6">Add some products to Firestore to see them here!</p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  onClick={handleDebugFirestore}
                  variant="outline"
                  className="border-pastel-dark text-pastel-accent hover:bg-pastel-light"
                >
                  🔍 Debug Firestore
                </Button>
                <Button
                  onClick={handleAddSampleProducts}
                  disabled={addingProducts}
                  className="bg-pastel-dark text-white hover:bg-pastel-accent disabled:opacity-50"
                >
                  {addingProducts ? (
                    <>
                      <div className="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Adding Products...
                    </>
                  ) : (
                    'Add Sample Products'
                  )}
                </Button>
              </div>
            </motion.div>
          ) : (
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              className="text-center text-xl text-pastel-accent/70 py-10"
            >
              No products match your current filters. Try adjusting them!
            </motion.p>
          )}
        </main>
      </div>
    </div>
  );
};

export default ShopPage;
